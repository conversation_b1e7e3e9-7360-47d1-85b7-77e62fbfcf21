.mainContainer {
    display: flex;
    flex-direction: column;
}

.rightContainerBody {
    width: 100%;
    height: 100%;
    padding: 30px;
    border-radius: 20px;
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

    @media screen and (max-width : 576px) {
        padding: 15px 5px;
        width: 100%;
        box-sizing: border-box;
        margin: 0;
    }
}

.body {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;

    @media screen and (max-width : 576px) {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.container {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: center;

    @media screen and (max-width : 576px) {
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 0;
        margin: 0;
        width: 100%;
    }
}

.payCard {
    display: flex;
    cursor: pointer;
    flex-wrap: wrap;
    gap: 12px;
    justify-content: flex-start;

    @media screen and (max-width : 1200px) {
        gap: 10px;
    }

    @media screen and (max-width : 768px) {
        gap: 8px;
        justify-content: center;
    }

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }
}

.noPayments {
    text-align: center;
    width: 100%;
    padding: 40px 20px;
}

.emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
}

.emptyIcon {
    font-size: 48px;
    opacity: 0.6;
}

.emptyState p {
    color: #64748b;
    font-size: 16px;
    font-weight: 500;
    margin: 0;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;

    @media screen and (max-width : 576px) {
        font-size: 24px;
        text-align: center;
        width: 100%;
    }
}



.searchWrapper {
    display: flex;
    width: 100%;
    margin-left: 10px;
    margin-bottom: 50px;
    border-bottom: 1px dashed gray;
}

.searchNumberContainer {
    display: flex;
    width: 266px;
    height: 40px;
    padding: 10px;
    align-items: center;
    gap: 10px;
    border-radius: 5px;
    background: #fff;
    border-color: 1px solid #000;
}

.searchNumber {
    border: none;
    outline: none;

}

.searchStatusContainer {
    display: inline-flex;
    height: 40px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    background: #fff;
    margin-left: 80px;
}

.searchStatus {
    border: none;
    outline: none;
    padding: 0px 20px;
}

.searchDateContainer {
    display: inline-flex;
    height: 40px;
    padding: 10px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5px;
    background: #fff;
    margin-left: 30px;
}

.searchDate {
    color: #000;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    border: none;
    outline: none;
}

.searchBtnContainer {

    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 30px;
    padding: 10px;
    border-radius: 5px;
    background: #4153ED;
    margin-left: 30px;
}

.searchButton {
    background-color: transparent;
    border: none;
    outline: none;
}

.addPayMethodContainer {
    width: 100%;
    margin-top: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 94%;
        margin: auto;
    }
}

.addPayMethod {
    width: 50%;

    @media screen and (max-width : 576px) {
        width: 100%;
        margin: auto;
        display: flex;
        justify-content: center;
    }
}

.AccWrapper {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 20px;
        align-items: center;
        text-align: center;
    }
}

.filterBtnContainer {
    display: flex;
    gap: 12px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 12px;
    }
}

.buttonIcon {
    margin-right: 8px;
    font-size: 16px;
}

.savedAddPayMethods {
    width: 250px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    outline: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.savedAddPayMethods:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.savedAddPayMethods:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.savedUserPayMethods {
    width: 250px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    outline: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.savedUserPayMethods:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.savedUserPayMethods:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.savedRecipientPayMethods {
    width: 250px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: none;
    outline: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.savedRecipientPayMethods:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.savedRecipientPayMethods:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(139, 92, 246, 0.3);
}

.AcCard {
    display: flex;
    flex-wrap: wrap;
    transition: all 0.3s ease;
    flex: 0 0 auto;

    @media screen and (max-width : 576px) {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        padding: 0;
    }
}

.displaypaymentCategory {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 25px;
    padding: 15px 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    border-radius: 12px;
    border-left: 4px solid;
    border-image: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%) 1;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.loader {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
}

.addPayModalContainer {
    width: 100%;
    height: 100vh;
    background: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;

    @media screen and (max-width : 576px) {
        padding: 15px;
    }
}

.addPayWrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    max-width: 700px;
    width: 100%;
    max-height: 85vh;
    overflow-y: auto;
    position: relative;

    @media screen and (max-width : 576px) {
        padding: 20px;
        max-height: 90vh;
        border-radius: 16px;
    }
}

.closeModalBtn {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(239, 68, 68, 0.3);
    z-index: 10;

    @media screen and (max-width : 576px) {
        top: 12px;
        right: 12px;
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
}

.closeModalBtn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.closeModalBtn:active {
    transform: scale(0.95);
}

.modalTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 25px;
    margin-top: 10px;
    text-align: center;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding-right: 60px;
    box-sizing: border-box;

    @media screen and (max-width : 576px) {
        font-size: 20px;
        margin-top: 20px;
        margin-bottom: 20px;
        padding-right: 70px;
        line-height: 1.3;
    }
}

.payoutPayBtn {
    padding: 12px 24px;
    border-radius: 12px;
    border: none;
    margin: 0px 10px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 10px;
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #475569;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and (max-width : 576px) {
        width: 200px;
        align-self: center;
        margin: 5px 0;
    }
}

.payoutPayBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.activeModalBtn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.topBtnSelector {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 10px;
    }
}

.addPayMethod {
    width: 100%;
    max-width: 100%;
    overflow: visible;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 30px;

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 0 10px;
        box-sizing: border-box;
    }
}

.filter {
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

    @media screen and (max-width : 576px) {
        width: 100%;
        padding: 20px;
    }
}

.filterIcon {
    margin-right: 8px;
    font-size: 18px;
}

.filterdialogue {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;

    @media screen and (max-width : 576px) {
        justify-content: center;
        text-align: center;
    }
}

.searchBodyCont {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 20px;

    @media screen and (max-width : 576px) {
        flex-direction: column;
        gap: 15px;
    }
}

.cover {
    margin-left: auto;
}

.searchBtn {
    min-width: 140px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;

    @media screen and (max-width : 576px) {
        width: 100%;
    }
}

.search {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border: none;
    color: white;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 14px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.search:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.search:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

.search:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.searchDisabled {
    width: 100%;
    height: 100%;
    padding: 12px 20px;
    border-radius: 12px;
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    border: none;
    color: white;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 14px;
    cursor: not-allowed;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0.7;
}

.rightBtns {
    margin-left: auto;
    padding-right: 80px;

    @media screen and (max-width : 576px) {
        margin-left: none;
        display: flex;
        flex-direction: column;
        width: 100%;

    }
}

.btnContOut {
    display: none;

    @media screen and (max-width : 576px) {
        display: block;
    }
}

.btnCont {
    /* display: none; */

    @media screen and (max-width : 576px) {
        display: block;
        width: 100%;
        background-color: #cac8c8;
        height: 40px;
        border-radius: 7px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 5px 0px;
    }
}

.mobAllBtn {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        transition: 0.1s ease-out;
    }
}

.mobAllBtnActive {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        background-color: #ffffff;
        border-radius: 10px;
        padding: 5px 10px;
        transition: 0.1s ease-out;
    }
}

.mobActiveBtn {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        transition: 0.1s ease-out;
    }
}

.mobActiveBtnActive {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        background-color: #ffffff;
        border-radius: 10px;
        padding: 5px 10px;
        transition: 0.1s ease-out;
    }
}

.mobDisabledBtn {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        transition: 0.1s ease-out;
    }
}

.mobDisabledBtnActive {
    @media screen and (max-width : 576px) {
        font-size: 13px;
        background-color: #ffffff;
        border-radius: 10px;
        padding: 5px 10px;
        transition: 0.1s ease-out;
    }
}


.firstNameInput input {
    border: none;
    background: transparent;
    width: 100%;
    height: 100%;
    outline: none;
    padding: 0 16px;
    font-size: 14px;
    color: #1e293b;
    font-family: 'Poppins', sans-serif;
}

.firstNameInput select,
.selectInput {
    border: none;
    background: transparent;
    width: 100%;
    height: 100%;
    outline: none;
    padding: 0 16px;
    font-size: 14px;
    color: #1e293b;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
}

.firstNameInput select:focus,
.selectInput:focus {
    outline: none;
}

.firstNameInput {
    flex: 1;
    min-width: 200px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    @media screen and (max-width : 576px) {
        width: 100%;
        min-width: unset;
        height: 48px; /* Maintain same height on mobile */
    }
}

.firstNameInput:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.loaderContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 20vh;
    margin: 0 auto;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.AcCard:hover {
    transform: translateY(-2px);
}

/* Mobile Header Section */
.mobileHeader {
    display: none;
}

@media (max-width: 576px) {
    .mobileHeader {
        display: block;
        margin-bottom: 20px;
        padding: 0 16px;
        text-align: center;
    }
}

.headerContent {
    margin-bottom: 24px;
    position: relative;
    z-index: 2;
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

@media (max-width: 576px) {
    .pageSubtitle {
        font-size: 13px;
    }
}